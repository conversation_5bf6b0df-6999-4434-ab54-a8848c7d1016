# 訂單 907 點數回饋問題排查報告

## 🔍 核心發現

**訂單 907 尚未執行點數回饋處理！**

## 1. 訂單基本信息

-   **訂單號**: B20250630Q0UHIAHU
-   **購買者 ID**: 1850 (張戊己)
-   **總金額**: 6,600 元
-   **商品**: 中階中脈優惠套餐 (CV 值: 3,740)
-   **商品類別**: 2 (非投資商品)
-   **處理時間**: **空白** ⚠️ (表示尚未執行 do_pointback)
-   **訂單狀態**: Complete

## 2. 關鍵人員分析

### 購買者

-   **ID**: 1850
-   **姓名**: 張戊己
-   **合夥等級**: 初級合夥人 (等級 4, 分潤比率 20%)
-   **當前點數**: 0

### 大總監 (user_id_executive_director)

-   **ID**: 1274
-   **姓名**: 陳美雲
-   **合夥等級**: 初級合夥人 (等級 4, 分潤比率 20%)
-   **當前點數**: 0
-   **問題**: ⚠️ 大總監和中心發起人是同一人

### 中心發起人 (user_id_center_founder)

-   **ID**: 1274
-   **姓名**: 陳美雲
-   **合夥等級**: 初級合夥人 (等級 4, 分潤比率 20%)
-   **當前點數**: 0
-   **問題**: ⚠️ 與大總監重複

### 其他關鍵角色

-   **中心主任**: 1294 (張芸熙) - 初級合夥人, 當前點數: 0
-   **中心**: 1904 (台南市中心) - 市級中心
-   **行政部門**: 1972 (廣告代表) - 當前點數: 0
-   **業務部門**: 1971 (業務代表) - 當前點數: 0

## 3. 問題分析

### 🚨 主要問題

1. **訂單尚未執行點數回饋**: `do_award_time` 欄位為空，這是根本原因
2. **大總監與中心發起人重複**: 都是 ID 1274 (陳美雲)
3. **等級設定可能不當**: 大總監只有「初級合夥人」等級 (通常應該更高)

### 💡 為什麼推廣獎勵和合夥平級獎會進入系統帳戶

根據代碼分析 (`BonusHelper.php`)，以下情況會導致獎勵進入系統帳戶：

1. **找不到有效的部門會員 ID 時**:

    ```php
    if ($user_id_marketing_dept > 0) {
        // 分配給行政部門
    } else {
        error_log("CV分配警告：未設定行政部門會員ID，金額將分配給系統帳戶");
        $this->add_available_cv(config('extra.skychakra.member_system'), ...);
    }
    ```

2. **合夥人權重計算結果為 0 時**:

    - 沒有有效的合夥人上線
    - 等級差異不符合分潤條件
    - 合夥人帳戶狀態異常

3. **CV 分配邏輯中的異常處理**

## 4. 代碼邏輯分析

### BonusHelper 中的 CV 分配邏輯

```php
// 當找不到有效的部門會員ID時，會分配給系統帳戶
if ($user_id_marketing_dept > 0) {
    // 分配給行政部門
} else {
    error_log("CV分配警告：未設定行政部門會員ID，金額將分配給系統帳戶");
    $this->add_available_cv(config('extra.skychakra.member_system'), $this->count_share_cv($marketing_amount));
}
```

### 合夥平級獎分配邏輯

-   需要找到有效的合夥人上線
-   需要符合等級差異條件
-   如果沒有符合條件的合夥人，獎勵可能進入系統帳戶

## 5. 建議解決方案

### 立即檢查項目

1. **執行點數回饋**: 訂單 907 尚未執行 `do_pointback`
2. **檢查人員設定**: 確認大總監和中心發起人的設定是否正確
3. **等級檢查**: 確認相關人員的合夥等級和中心等級設定

### 具體操作建議

1. **手動執行點數回饋**:

    ```
    http://localhost:8888/order/order_ctrl/do_pointback?id=907
    ```

2. **檢查合夥等級設定**:

    - 確認大總監應該有更高的合夥等級
    - 檢查合夥等級的分潤比率設定

3. **檢查中心等級設定**:
    - 確認中心發起人的中心等級設定
    - 檢查中心等級的權限和分潤條件

## 6. 預期分配結果

基於 CV 值 3,740 和系統設定，預期分配應該是：

### 大總監應得

-   根據合夥等級的分潤比率計算
-   如果是高級合夥人，應該有相應的 CV 分配

### 中心發起人應得

-   根據中心等級的分潤比率計算
-   中心發起人應該有中心相關的獎勵

### 推廣獎勵

-   應該分配給推薦人或上線合夥人
-   不應該全部進入系統帳戶

### 合夥平級獎

-   應該分配給同等級或更高等級的合夥人
-   需要檢查上線結構中的合夥人等級

## 7. 📊 合夥等級系統分析

### 當前合夥等級設定

| 等級 | 名稱       | 投資門檻 | 分潤比率 | 廣告權重 |
| ---- | ---------- | -------- | -------- | -------- |
| 1    | 微合夥人   | 132      | 5%       | 1        |
| 2    | 創業合夥人 | 440      | 10%      | 2        |
| 3    | 準合夥人   | 1,320    | 15%      | 4        |
| 4    | 初級合夥人 | 4,400    | 20%      | 8        |
| 5    | 高級合夥人 | 13,200   | 40%      | 16       |
| 6    | 區級合夥人 | 44,000   | 60%      | 32       |
| 7    | 市級合夥人 | 132,000  | 80%      | 64       |
| 8    | 省級合夥人 | 440,000  | 100%     | 128      |

### 🎯 問題分析

-   **大總監 (陳美雲)**: 等級 4 (初級合夥人, 20%)
-   **中心發起人 (陳美雲)**: 同一人，等級 4
-   **建議**: 大總監通常應該是更高等級 (如等級 6 以上)

## 8. 🔧 解決方案與檢查清單

### ⚡ 立即執行

1. **執行點數回饋**: `http://localhost:8888/order/order_ctrl/do_pointback?id=907`

### 📋 執行後檢查項目

-   [ ] 確認 do_award_time 是否更新
-   [ ] 檢查陳美雲 (ID: 1274) 的點數變化
-   [ ] 確認其他相關人員的點數
-   [ ] 驗證系統帳戶是否還有異常進帳
-   [ ] 檢查分配邏輯是否符合預期

### 📈 預期分配結果 (基於 CV 值 3,740)

-   **大總監應得**: 約 748 點 (3,740 × 20%)
-   **推廣獎勵**: 應分配給符合條件的上線
-   **合夥平級獎**: 根據權重分配給合夥人

---

**🕒 報告生成時間**: 2025-08-25
**📋 分析對象**: 訂單 907 (B20250630Q0UHIAHU)
**⚠️ 狀態**: 待執行點數回饋處理
**👤 客戶**: 張戊己 (ID: 1850)
